*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'C:\Keil_v5\ARM\ARMCC\Bin'
Build target 'INS_4000'
compiling gd32f4xx_it.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling main.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling systick.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling imu_data.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling INS_Data.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling can_data.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling Time_Unify.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling clock.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling Data_shift.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling INS_Sys.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling fpgad.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gdwatch.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling INS_Output.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling INS_Init.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling api_ch392.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling datado.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bmp2.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bmp280.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bsp_adc.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bsp_can.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bsp_exti.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bsp_flash.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bsp_fmc.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bsp_fwdgt.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bsp_gpio.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bsp_rtc.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bsp_sys.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bsp_tim.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bsp_uart.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling CH378_HAL.C...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling CH395CMD.C...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling CH395SPI.C...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling common.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling FILE_SYS.C...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling Logger.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling TCP_Server.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling CH378_SPI_HW.C...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling bsp_soft_i2c_master.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling drv_spi.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling drv_gpio.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling data_convert.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling SEGGER_RTT.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling nav_cli.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling SEGGER_RTT_printf.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling adxl355.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling private_math.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling readpaoche.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling navi.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling align.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling AnnTempCompen.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling kalman.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling matvecmath.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling dynamic_align.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling read_and_check_gnss_data.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling computerFrameParse.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling protocol.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling frame_analysis.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling InsTestingEntry.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling SetParaBao.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling pjt_board.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling sensor_misc.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling app_tool.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling system_gd32f4xx.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
assembling startup_gd32f450_470.s...
startup_gd32f450_470.s: Error: A9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)
startup_gd32f450_470.s: startup_gd32f450_470.s: Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:startup_gd32f450_470.s:  http://www.keil.com/support/man/docs/license/license_management.htmstartup_gd32f450_470.s:  If you need further help, provide this complete error report to your <NAME_EMAIL>_gd32f450_470.s:  - ARMLMD_LICENSE_FILE: unsetstartup_gd32f450_470.s:  - LM_LICENSE_FILE: unsetstartup_gd32f450_470.s:  - ARM_TOOL_VARIANT: unsetstartup_gd32f450_470.s:  - ARM_PRODUCT_PATH: unsetstartup_gd32f450_470.s:  - Product location: C:\Keil_v5\ARM\sw\mappingsstartup_gd32f450_470.s:  - Toolchain location: C:\Keil_v5\ARM\ARMCC\Binstartup_gd32f450_470.s:  - Selected tool variant: mdk_stdstartup_gd32f450_470.s:  - Checkout feature: LIC0=KA...-.....-.....startup_gd32f450_470.s:  - Feature version: 5.0202006startup_gd32f450_470.s:  - Keil error code: 1startup_gd32f450_470.s: Product: MDK Plus 5.36startup_gd32f450_470.s: Component: ARM Compiler 5.06 update 7 (build 960)startup_gd32f450_470.s: Tool: ArmAsm [4d35fa]compiling gd32f4xx_adc.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_can.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_crc.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_ctc.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_dac.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_dbg.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_dci.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_dma.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_enet.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_exmc.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_exti.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_fmc.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_fwdgt.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_gpio.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_i2c.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_iref.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_ipa.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_misc.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_pmu.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_rcu.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_rtc.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_sdio.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_spi.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_syscfg.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_timer.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_tli.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_trng.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_usart.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
compiling gd32f4xx_wwdgt.c...
Error: C9555E: Failed to check out a license.LICENSE ERROR (R207(3): REGISTRY READ ERROR)

Check that your license details are correct in the License Management dialog of MDK. Additional information is available at:
 http://www.keil.com/support/man/docs/license/license_management.htm
 If you need further help, provide this complete error report to your <NAME_EMAIL>.
 - ARMLMD_LICENSE_FILE: unset
 - LM_LICENSE_FILE: unset
 - ARM_TOOL_VARIANT: unset
 - ARM_PRODUCT_PATH: unset
 - Product location: C:\Keil_v5\ARM\sw\mappings
 - Toolchain location: C:\Keil_v5\ARM\ARMCC\Bin
 - Selected tool variant: mdk_std
 - Checkout feature: LIC0=KA...-.....-.....
 - Feature version: 5.0202006
 - Keil error code: 1
Product: MDK Plus 5.36
Component: ARM Compiler 5.06 update 7 (build 960)
Tool: ArmCC [4d365d]
".\Objects\arm2.axf" - 93 Error(s), 0 Warning(s).
Target not created.
Build Time Elapsed:  00:00:10
