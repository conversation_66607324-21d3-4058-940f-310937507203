//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：SetParaBao.h
// 文件标识：参数设置和软件升级协议头文件
// 文件摘要：定义参数设置和升级相关的数据结构和函数
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.11.20
// 移植适配：INS370M-25J20240919项目
//---------------------------------------------------------
#ifndef __SETPARABAO_H__
#define __SETPARABAO_H__

#include <stdint.h>
#include "gd32f4xx.h"

// 基本配置定义
#define SETPARA_DATAOUT_FPGA_FREQ               200  // FPGA的硬件中断频率
#define SETPARA_RXBUFFER_DMA_SIZE               256	
#define SETPARA_TXBUFFER_DMA_SIZE               64
#define SAVE_SET_PARA_ADDR                      0x08050000  // 参数保存地址(使用SECTOR_6)

// 包头定义
#define SETPARA_HEADER_0                        (0xFA)
#define SETPARA_HEADER_1                        (0x55)
#define SETPARA_HEADER_2                        (0xAF)

// 包尾定义
#define SETPARA_END_0                           (0x00)
#define SETPARA_END_1                           (0xFF)

// 协议报文类型 - 上位机发送，下位机接收
#define SETPARA_TYPE0_output                    (0x11AA)  // 输出参数
#define SETPARA_TYPE0_baud                      (0x13AA)  // 波特率
#define SETPARA_TYPE0_frequency                 (0x14AA)  // 数据输出频率
#define SETPARA_TYPE0_gnss                      (0x18AA)  // 设置GNSS杆臂参数
#define SETPARA_TYPE0_angle                     (0x19AA)  // 设置天线安装角度
#define SETPARA_TYPE0_vector                    (0x1AAA)  // 惯导-后轮轴中心位置矢量
#define SETPARA_TYPE0_deviation                 (0x1BAA)  // 惯导角度安装偏差
#define SETPARA_TYPE0_initvalue                 (0x21AA)  // GNSS初始值
#define SETPARA_TYPE0_coord                     (0x1EAA)  // 设置用户坐标轴
#define SETPARA_TYPE0_offsetime                 (0x25AA)  // 设置静态测零偏时间
#define SETPARA_TYPE0_transfer                  (0xCCAA)  // 设置透传协议
#define SETPARA_TYPE0_solidify                  (0xF1AA)  // 固化参数
#define SETPARA_TYPE0_factory                   (0xF2AA)  // 恢复出厂设置
#define SETPARA_TYPE0_setpara                   (0xF3AA)  // 设置所有参数
#define SETPARA_TYPE0_readpara                  (0xF4AA)  // 参数回读
#define SETPARA_TYPE0_readver                   (0xF5AA)  // 读取版本号
#define SETPARA_TYPE0_gps                       (0xC3AA)  // GPS中断类型
#define SETPARA_TYPE0_type                      (0xC4AA)  // 数据输出Type
#define SETPARA_TYPE0_debug                     (0xC1AA)  // 是否开启Debug模式
#define SETPARA_TYPE0_gyro                      (0xC2AA)  // 陀螺类型
#define SETPARA_TYPE0_calibration               (0x30AA)  // 标定参数
#define SETPARA_TYPE0_temoffset                 (0x34AA)  // 温度补偿
#define SETPARA_TYPE0_KalmanQ                   (0x31AA)  // 卡尔曼滤波Q矩阵
#define SETPARA_TYPE0_KalmanR                   (0x32AA)  // 卡尔曼滤波R矩阵
#define SETPARA_TYPE0_filter                    (0x33AA)  // 间接滤波校正系数
#define SETPARA_TYPE0_FactorGyro                (0x35AA)  // 陀螺标定因数
#define SETPARA_TYPE0_FactorAcc                 (0x36AA)  // 加计标定因数
#define SETPARA_TYPE0_UPDATE_START              (0x51AA)  // 软件升级开始命令
#define SETPARA_TYPE0_UPDATE_SEND               (0x52AA)  // 发送升级包命令
#define SETPARA_TYPE0_UPDATE_END                (0x55AA)  // 升级包完成命令
#define SETPARA_TYPE0_UPDATE_STOP               (0x59AA)  // 升级终止命令

// 协议报文类型 - 下位机发送，上位机接收
#define SETPARA_TYPE1_output                    (0x11AA)
#define SETPARA_TYPE1_baud                      (0x13AA)
#define SETPARA_TYPE1_frequency                 (0x14AA)
#define SETPARA_TYPE1_gnss                      (0x18AA)
#define SETPARA_TYPE1_angle                     (0x19AA)
#define SETPARA_TYPE1_vector                    (0x1AAA)
#define SETPARA_TYPE1_deviation                 (0x1BAA)
#define SETPARA_TYPE1_initvalue                 (0x21AA)
#define SETPARA_TYPE1_coord                     (0x1EAA)
#define SETPARA_TYPE1_offsetime                 (0x25AA)
#define SETPARA_TYPE1_transfer                  (0xCCAA)
#define SETPARA_TYPE1_solidify                  (0xF1AA)
#define SETPARA_TYPE1_factory                   (0xF2AA)
#define SETPARA_TYPE1_setpara                   (0xF3AA)
#define SETPARA_TYPE1_readpara                  (0xF4AA)
#define SETPARA_TYPE1_readver                   (0xF5AA)
#define SETPARA_TYPE1_gps                       (0xC3AA)
#define SETPARA_TYPE1_type                      (0xC4AA)
#define SETPARA_TYPE1_debug                     (0xC1AA)
#define SETPARA_TYPE1_gyro                      (0xC2AA)
#define SETPARA_TYPE1_calibration               (0x30AA)
#define SETPARA_TYPE1_temoffset                 (0x34AA)
#define SETPARA_TYPE1_KalmanQ                   (0x31AA)
#define SETPARA_TYPE1_KalmanR                   (0x32AA)
#define SETPARA_TYPE1_filter                    (0x33AA)
#define SETPARA_TYPE1_FactorGyro                (0x35AA)
#define SETPARA_TYPE1_FactorAcc                 (0x36AA)
#define SETPARA_TYPE1_UPDATE_START              (0x51AA)
#define SETPARA_TYPE1_UPDATE_SEND               (0x52AA)
#define SETPARA_TYPE1_UPDATE_END                (0x55AA)
#define SETPARA_TYPE1_UPDATE_STOP               (0x59AA)

// 软件版本信息
#define VERMAIN                                 (0x01)    // 主版本号
#define VERMINOR                                (0x01)    // 次版本号
#define REVISION                                (0x01)    // 修订号
#define VERYEAR                                 (2025)    // 年份	
#define VERMONTH                                (3)       // 月份
#define VERDAY                                  (5)       // 日份
#define PLAN                                    (0)       // 方案
#define SUFFIX                                  (1)       // 后缀
#define CUSTOM                                  (2)       // 客户定制版

// 升级相关地址定义（从INS600-21A移植）
#define APP_UPDATE_ADDRESS                      (0x08100000)  // 升级区域地址
#define APP_UPDATE_ADDRESS1                     (0x08180000)  // 升级区域地址1
#define APP_UPDATE_CFG_ADDR                     (0x081F0000)  // 升级配置地址

#pragma pack(1)

// 包头结构
typedef struct _parabaghead
{
    unsigned char header[3];    // 报文头固定:0xFA，0x55，0xAF
    unsigned short Type;        // 报文类型
    unsigned short len;         // 报文长度
} parabaghead, *p_parabaghead;

// 包尾结构
typedef struct _parabagend
{
    unsigned char check;        // 校验码
    unsigned char ender[2];     // 报文尾固定:0x00 0xFF
} parabagend, *p_parabagend;

// 通用信息体结构
typedef struct _parabag_info_Other
{
    unsigned char BackFlag;     // 反馈标志
    unsigned char reserve[245]; // 预留
} parabag_info_Other, *p_parabag_info_Other;

// 通用回应数据包结构
typedef struct _parabag_Other_back
{
    parabaghead head;           // 包头
    parabag_info_Other info;    // 信息体
    parabagend ender;           // 包尾
} parabag_Other_back, *p_parabag_Other_back;

// 波特率信息体结构
typedef struct _parabag_info_Setbaud
{
    unsigned short SetbaudPara; // 设置波特率
    unsigned char reserve[244]; // 预留
} parabag_info_Setbaud, *p_parabag_info_Setbaud;

// 波特率数据包结构
typedef struct _parabag_Setbaud
{
    parabaghead head;           // 包头
    parabag_info_Setbaud info;  // 信息体
    parabagend ender;           // 包尾
} parabag_Setbaud, *p_parabag_Setbaud;

// 数据输出频率信息体结构
typedef struct _parabag_info_Setfrequency
{
    unsigned short SetfrePara;  // 输出频率
    unsigned char reserve[244]; // 预留
} parabag_info_Setfrequency, *p_parabag_info_Setfrequency;

// 数据输出频率数据包结构
typedef struct _parabag_Setfrequency
{
    parabaghead head;               // 包头
    parabag_info_Setfrequency info; // 信息体
    parabagend ender;               // 包尾
} parabag_Setfrequency, *p_parabag_Setfrequency;

// 升级开始信息体结构
typedef struct _parabag_info_UpdateStart
{
    unsigned char BackFlag;     // 反馈标志
    unsigned char Vermain;      // 主版本号
    unsigned char Verminor;     // 次版本号
    unsigned char Revision;     // 修订号
    unsigned int VerDate;       // 版本日期
    unsigned char Plan;         // 方案
    unsigned char Suffix;       // 后缀
    unsigned char Custom;       // 客户定制版
    unsigned short Count;       // 帧计数
    unsigned char reserve[235]; // 预留
} parabag_info_UpdateStart, *p_parabag_info_UpdateStart;

// 升级开始回应数据包结构
typedef struct _parabag_UpdateStart_back
{
    parabaghead head;                   // 包头
    parabag_info_UpdateStart info;      // 信息体
    parabagend ender;                   // 包尾
} parabag_UpdateStart_back, *p_parabag_UpdateStart_back;

// 升级发送信息体结构
typedef struct _parabag_info_UpdateSend
{
    unsigned short BaoIndex;    // 当前包编号
    unsigned short TotalBao;    // 总包数量
    unsigned char Length;       // 当前有效数据长度
    unsigned char UpdateData[128]; // 升级数据
    unsigned char reserve[111]; // 预留
    unsigned short Count;       // 帧计数
} parabag_info_UpdateSend, *p_parabag_info_UpdateSend;

// 升级发送回应信息体结构
typedef struct _parabag_info_UpdateSend_back
{
    unsigned char BackFlag;     // 反馈标志
    unsigned short Count;       // 帧计数
    unsigned char reserve[243]; // 预留
} parabag_info_UpdateSend_back, *p_parabag_info_UpdateSend_back;

// 升级发送回应数据包结构
typedef struct _parabag_UpdateSend_back
{
    parabaghead head;                       // 包头
    parabag_info_UpdateSend_back info;      // 信息体
    parabagend ender;                       // 包尾
} parabag_UpdateSend_back, *p_parabag_UpdateSend_back;

// 升级完成回应数据包结构
typedef struct _parabag_UpdateEnd_back
{
    parabaghead head;           // 包头
    parabag_info_Other info;    // 信息体
    parabagend ender;           // 包尾
} parabag_UpdateEnd_back, *p_parabag_UpdateEnd_back;

// 升级终止回应数据包结构
typedef struct _parabag_UpdateStop_back
{
    parabaghead head;           // 包头
    parabag_info_Other info;    // 信息体
    parabagend ender;           // 包尾
} parabag_UpdateStop_back, *p_parabag_UpdateStop_back;

// 参数回读回应数据包结构
typedef struct _parabag_ReadPara_back
{
    parabaghead head;           // 包头
    parabag_info_Other info;    // 信息体
    parabagend ender;           // 包尾
} parabag_ReadPara_back, *p_parabag_ReadPara_back;

// 通用设置类型数据包结构（用于简单命令）
typedef struct _parabag_SetType
{
    parabaghead head;           // 包头
    parabag_info_Other info;    // 信息体
    parabagend ender;           // 包尾
} parabag_SetType, *p_parabag_SetType;

// 升级发送数据包结构
typedef struct _parabag_UpdateSend
{
    parabaghead head;                   // 包头
    parabag_info_UpdateSend info;       // 信息体
    parabagend ender;                   // 包尾
} parabag_UpdateSend, *p_parabag_UpdateSend;

// 参数数据结构
typedef struct _Setpara_Data
{
    unsigned short Flag;        // 标志位
    unsigned short Setbaud;     // 波特率
    unsigned short Setfre;      // 频率
    float armX, armY, armZ;     // GNSS杆臂参数
    float angleX, angleY, angleZ; // 天线安装角度
    float vectorX, vectorY, vectorZ; // 位置矢量
    float pitch, roll, Course;  // 安装偏差角度
    unsigned char SetCoordPara; // 坐标设置
    unsigned char SetDirPara;   // 方向设置
    unsigned short SetTimepara; // 静态测零偏时间
    unsigned char reserve[200]; // 预留空间
} Setpara_Data, *p_Setpara_Data;

// UART DMA结构体定义
typedef struct _dmauart
{
    uint8_t *rxbuffer;          // 接收缓冲区
    uint16_t rxsize;            // 接收大小
    uint8_t *txbuffer;          // 发送缓冲区
    uint16_t txsize;            // 发送大小
} dmauart_t, *p_dmauart_t;

#pragma pack()

// 全局变量声明
extern uint8_t g_UpdateBackFlag;
extern uint8_t g_UpdateSuccessful;
extern uint8_t g_ucSystemResetFlag;
extern uint8_t g_UpdateFinishSuccessful;
extern uint8_t g_VersionQueryFlag;
extern uint8_t g_StartUpdateFirm;
extern Setpara_Data stSetPara;

// 函数声明
void SetParaBao_Init(void);
void ReadParaFromFlash(void);
void SaveParaToFlash(void);
void InitParaToAlgorithm(void);

// 帧头帧尾设置函数
void SendPara_SetHead(p_parabag_Other_back stPara, uint16_t Type, uint16_t Len);
void SendPara_SetEnd(p_parabag_Other_back stPara, uint16_t Len);
void UpdateStart_SetHead(p_parabag_UpdateStart_back stPara, uint16_t Type, uint16_t Len);
void UpdateStart_SetEnd(p_parabag_UpdateStart_back stPara, uint16_t Len);
void UpdateSend_SetHead(p_parabag_UpdateSend_back stPara, uint16_t Type, uint16_t Len);
void UpdateSend_SetEnd(p_parabag_UpdateSend_back stPara, uint16_t Len);
void UpdateEnd_SetHead(p_parabag_UpdateEnd_back stPara, uint16_t Type, uint16_t Len);
void UpdateEnd_SetEnd(p_parabag_UpdateEnd_back stPara, uint16_t Len);
void UpdateStop_SetHead(p_parabag_UpdateStop_back stPara, uint16_t Type, uint16_t Len);
void UpdateStop_SetEnd(p_parabag_UpdateStop_back stPara, uint16_t Len);
void ReadPara_SetHead(p_parabag_ReadPara_back stPara, uint16_t Type, uint16_t Len);
void ReadPara_SetEnd(p_parabag_ReadPara_back stPara, uint16_t Len);

// CRC校验函数
uint8_t crc_verify_8bit(uint8_t *data, uint16_t length);

// 参数设置函数
void SetParaBaud(p_dmauart_t pdmauart);
void SetParaFrequency(p_dmauart_t pdmauart);

// 升级相关函数
void SetParaUpdateStart(p_dmauart_t pdmauart);
void SetParaUpdateSend(p_dmauart_t pdmauart);
void SetParaUpdateEnd(p_dmauart_t pdmauart);
void SetParaUpdateStop(p_dmauart_t pdmauart);

// 帧头帧尾设置函数
void SendPara_SetHead(p_parabag_Other_back stPara,uint16_t Type,uint16_t Len);
void SendPara_SetEnd(p_parabag_Other_back stPara,uint16_t Len);
void UpdateStart_SetHead(p_parabag_UpdateStart_back stPara,uint16_t Type,uint16_t Len);
void UpdateStart_SetEnd(p_parabag_UpdateStart_back stPara,uint16_t Len);

// Flash操作适配函数
void Drv_FlashErase(uint32_t addr, uint8_t flag);
void Drv_FlashWrite(uint8_t* data, uint32_t addr, uint16_t len, uint8_t flag);
void Drv_SystemReset(void);

// 主流程控制函数
void UartDmaRecSetPara(p_dmauart_t pdmauart);

// 版本和参数管理函数
void SetParaReadVersion(p_dmauart_t pdmauart);
void SetParaReadPara(p_dmauart_t pdmauart);
void SetParaSolidify(p_dmauart_t pdmauart);
void SetParaFactory(p_dmauart_t pdmauart);

// 调试函数
void SendSimpleVersionInfo(void);

#endif /* __SETPARABAO_H__ */
