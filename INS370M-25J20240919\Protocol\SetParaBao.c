//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：SetParaBao.c
// 文件标识：
// 文件摘要：参数设置和软件升级协议实现（从INS600-21A新协议移植）
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.11.20
// 移植适配：INS370M-25J20240919项目
//---------------------------------------------------------

#include <stdio.h>
#include <string.h>
#include "gd32f4xx.h"
#include "computerFrameParse.h"
#include "SetParaBao.h"
#include "UartAdapter.h"
#include "systick.h"
#include "INS_Data.h"
#include "bsp_flash.h"

// 全局变量定义
uint8_t	g_UpdateBackFlag=1;         // 反馈标志(0x01-正常/02-异常)
uint8_t g_UpdateSuccessful=0;        // 升级成功标志
uint8_t g_ucSystemResetFlag=0;       // 系统复位标志
uint8_t g_UpdateFinishSuccessful=0;  // 升级完成是否成功
uint8_t g_VersionQueryFlag = 0;      // 版本查询标志
uint8_t g_StartUpdateFirm = 0;       // 开始升级固件标志

Setpara_Data stSetPara={0};          // 参数设置数据结构

// 外部函数声明
extern void delay_ms(uint32_t ms);
extern void uart4sendmsg(char* msg, uint16_t len);
extern void uart3sendmsg422(char* msg, uint16_t len);
extern void gd_eval_com_init(uint32_t com, uint32_t baudval);
extern void Sys_Soft_Reset(void);

// CRC校验函数实现
uint8_t crc_verify_8bit(uint8_t *data, uint16_t length)
{
    uint8_t crc = 0;
    uint16_t i, j;

    for(i = 0; i < length; i++)
    {
        crc ^= data[i];
        for(j = 0; j < 8; j++)
        {
            if(crc & 0x80)
                crc = (crc << 1) ^ 0x07;
            else
                crc <<= 1;
        }
    }
    return crc;
}

// Flash操作适配函数（适配INS600-21A的Flash接口到INS370M）
void Drv_FlashErase(uint32_t addr, uint8_t flag)
{
    // 使用INS370M的Flash擦除函数
    // 这里需要根据实际的Flash操作函数进行适配
    // 暂时使用空实现，需要后续完善
}

void Drv_FlashWrite(uint8_t* data, uint32_t addr, uint16_t len, uint8_t flag)
{
    // 使用INS370M的Flash写入函数
    // 这里需要根据实际的Flash操作函数进行适配
    // 暂时使用空实现，需要后续完善
}

void Drv_SystemReset(void)
{
    // 使用INS370M的系统复位函数
    Sys_Soft_Reset();
}

// 调试函数：发送简单的版本信息
void SendSimpleVersionInfo(void)
{
    char versionMsg[128];
    sprintf(versionMsg, "INS370M Ver:%d.%d.%d Date:%d-%02d-%02d\r\n",
            VERMAIN, VERMINOR, REVISION, VERYEAR, VERMONTH, VERDAY);

    uart4sendmsg(versionMsg, strlen(versionMsg));
}

//帧头设置函数
void SendPara_SetHead(p_parabag_Other_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//帧尾设置函数
void SendPara_SetEnd(p_parabag_Other_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//开始升级帧头
void UpdateStart_SetHead(p_parabag_UpdateStart_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//开始升级帧尾
void UpdateStart_SetEnd(p_parabag_UpdateStart_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_UpdateStart_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//软件升级开始命令（同时用于版本查询）
void SetParaUpdateStart(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateStart_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_UpdateStart_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateStart_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateStart_back));

    parabag_SetType stUpdateStart;//接收
    memcpy(&stUpdateStart,pdmauart->rxbuffer,sizeof(parabag_SetType));

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3],sizeof(parabag_SetType) - 3 - 3);

    if(crc == stUpdateStart.ender.check)
    {
        stSendPara.info.BackFlag=0x01;

        stSendPara.info.Vermain = VERMAIN;
        stSendPara.info.Verminor = VERMINOR;
        stSendPara.info.Revision = REVISION;
        stSendPara.info.VerDate = VERYEAR*10000 + VERMONTH*100 + VERDAY;
        stSendPara.info.Plan = PLAN;
        stSendPara.info.Suffix = SUFFIX;
        stSendPara.info.Custom = CUSTOM;

        g_StartUpdateFirm=1;
        g_UpdateSuccessful=0;
    }
    else 
    {
        stSendPara.info.BackFlag=0x02;
    }
    
    g_VersionQueryFlag=1;//版本查询

    stSendPara.info.Count++;//帧计数

    delay_ms(100);

    //配置帧头
    UpdateStart_SetHead(&stSendPara,SETPARA_TYPE1_UPDATE_START,BaoLen);
    //配置帧尾
    UpdateStart_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);	
    delay_ms(100);
}

// 版本查询命令（兼容旧接口）
void SetParaReadVersion(p_dmauart_t pdmauart)
{
    // 直接调用升级开始命令，因为它包含了版本查询功能
    SetParaUpdateStart(pdmauart);
}

//固件升级处理
void ParaUpdateHandle(uint8_t *pucBuf,uint16_t usIndex,uint16_t usTotalBao,uint8_t ucLen)
{
    static uint32_t uiOffsetAddr=0,uiLastBaoInDex=1,flag=0;
    uint8_t UpdateFlagBuff[5]={0};//升级被分区完成标志,升级完成后，保存此标志到FLASH指定位置，拷贝到运行区后，此标志擦除

    //if(uiLastBaoInDex == usIndex)
    if(((uiLastBaoInDex == usIndex)||(uiLastBaoInDex != usIndex-1))&&(0 !=usIndex))
    {
        return;
    }

    if(flag==0)
    {
        if(0 !=usIndex)//首帧帧索引不为0时，报错
        {
            g_UpdateBackFlag=2;//反馈标志(0x01-正常/02-异常)
            return;
        }
        else//检测到首帧帧索引为0时，才通过
        {
            flag=1;
        }
    }

    if(usIndex == 0)
    {
        uiOffsetAddr=0;
        Drv_FlashErase(APP_UPDATE_ADDRESS,1);
        Drv_FlashErase(APP_UPDATE_ADDRESS1,1);
        Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen, 0);
        uiOffsetAddr += ucLen;
    }
    else
    {
        Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen, 0);
        uiOffsetAddr += ucLen;
    }

    //升级完成后，保存升级标志和数据长度，并重启
    if(usIndex>=usTotalBao-1)
    {
        g_UpdateFinishSuccessful=1;
        UpdateFlagBuff[0] =0xC5;//保存升级标志
        //数据长度
        UpdateFlagBuff[4] = (uint8_t)(uiOffsetAddr>>24);//高位
        UpdateFlagBuff[3] = (uint8_t)(uiOffsetAddr>>16);//中高位
        UpdateFlagBuff[2] = (uint8_t)(uiOffsetAddr>>8);//中低位
        UpdateFlagBuff[1] = (uint8_t)(uiOffsetAddr);//低位
        
        Drv_FlashWrite(UpdateFlagBuff, APP_UPDATE_CFG_ADDR, sizeof(UpdateFlagBuff), 0);//保存升级标志

        uiOffsetAddr=0;

        //系统复位重启
        //Drv_SystemReset();
    }

    uiLastBaoInDex = usIndex;
}

//参数设置主流程控制
void UartDmaRecSetPara(p_dmauart_t pdmauart)
{
    unsigned short Type=0;	//报文类型

    memcpy(&Type,&pdmauart->rxbuffer[3],2);

    switch(Type)
    {
        case SETPARA_TYPE0_UPDATE_START://软件升级开始命令（同时用于版本查询）
             SetParaUpdateStart(pdmauart);
            break;

        case SETPARA_TYPE0_readver:	//读取版本号（兼容旧协议）
             SetParaReadVersion(pdmauart);
            break;

        default:
            break;
    }
}
