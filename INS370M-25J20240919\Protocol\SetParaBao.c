//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：SetParaBao.c
// 文件标识：
// 文件摘要：参数设置和软件升级协议实现（从INS600-21A新协议移植）
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.11.20
// 移植适配：INS370M-25J20240919项目
//---------------------------------------------------------

#include <stdio.h>
#include <string.h>
#include "gd32f4xx.h"
#include "computerFrameParse.h"
#include "SetParaBao.h"
#include "UartAdapter.h"
#include "systick.h"
#include "INS_Data.h"
#include "bsp_flash.h"

// 全局变量定义
uint8_t	g_UpdateBackFlag=1;         // 反馈标志(0x01-正常/02-异常)
uint8_t g_UpdateSuccessful=0;        // 升级成功标志
uint8_t g_ucSystemResetFlag=0;       // 系统复位标志
uint8_t g_UpdateFinishSuccessful=0;  // 升级完成是否成功
uint8_t g_VersionQueryFlag = 0;      // 版本查询标志
uint8_t g_StartUpdateFirm = 0;       // 开始升级固件标志

Setpara_Data stSetPara={0};          // 参数设置数据结构

// 坐标轴设置常量（从INS600-21A移植）
static const char SetCoord[6][3] =
{
    {'X','Y','Z'},
    {'X','Z','Y'},
    {'Y','X','Z'},
    {'Y','Z','X'},
    {'Z','X','Y'},
    {'Z','Y','X'},
};

static const float SetDir[8][3] =
{
    {1.0,1.0,1.0},
    {-1.0,1.0,1.0},
    {1.0,-1.0,1.0},
    {1.0,1.0,-1.0},
    {-1.0,-1.0,1.0},
    {1.0,-1.0,-1.0},
    {-1.0,1.0,-1.0},
    {-1.0,-1.0,-1.0},
};

// 外部函数声明
extern void delay_ms(uint32_t ms);
extern void uart4sendmsg(char* msg, uint16_t len);
extern void uart3sendmsg422(char* msg, uint16_t len);
extern void gd_eval_com_init(uint32_t com, uint32_t baudval);
extern void Sys_Soft_Reset(void);

// Flash操作适配函数（适配INS600-21A的Flash接口到INS370M）
void Drv_FlashErase(uint32_t addr, uint8_t flag)
{
    // 使用INS370M的Flash擦除函数
    // 这里需要根据实际的Flash操作函数进行适配
    // 暂时使用空实现，需要后续完善
}

void Drv_FlashWrite(uint8_t* data, uint32_t addr, uint16_t len, uint8_t flag)
{
    // 使用INS370M的Flash写入函数
    // 这里需要根据实际的Flash操作函数进行适配
    // 暂时使用空实现，需要后续完善
}

void Drv_SystemReset(void)
{
    // 使用INS370M的系统复位函数
    Sys_Soft_Reset();
}

//帧头设置函数（从INS600-21A移植）
void SendPara_SetHead(p_parabag_Other_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//帧尾设置函数（从INS600-21A移植）
void SendPara_SetEnd(p_parabag_Other_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//开始升级帧头（从INS600-21A移植）
void UpdateStart_SetHead(p_parabag_UpdateStart_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//开始升级帧尾（从INS600-21A移植）
void UpdateStart_SetEnd(p_parabag_UpdateStart_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 调试函数：发送简单的版本信息
void SendSimpleVersionInfo(void)
{
    char versionMsg[128];
    sprintf(versionMsg, "INS370M Ver:%d.%d.%d Date:%d-%02d-%02d\r\n",
            VERMAIN, VERMINOR, REVISION, VERYEAR, VERMONTH, VERDAY);

    uart4sendmsg(versionMsg, strlen(versionMsg));
}

// CRC校验函数实现
uint8_t crc_verify_8bit(uint8_t *data, uint16_t length)
{
    uint8_t crc = 0;
    uint16_t i, j;

    for(i = 0; i < length; i++)
    {
        crc ^= data[i];
        for(j = 0; j < 8; j++)
        {
            if(crc & 0x80)
                crc = (crc << 1) ^ 0x07;
            else
                crc <<= 1;
        }
    }
    return crc;
}

// 通用帧头设置
void SendPara_SetHead(p_parabag_Other_back stPara, uint16_t Type, uint16_t Len)
{
    stPara->head.header[0] = SETPARA_HEADER_0;
    stPara->head.header[1] = SETPARA_HEADER_1;
    stPara->head.header[2] = SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

// 通用帧尾设置
void SendPara_SetEnd(p_parabag_Other_back stPara, uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};
    
    memcpy(Databuf, stPara, Len-3);
    
    stPara->ender.check = crc_verify_8bit(&Databuf[3], Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 升级开始帧头设置
void UpdateStart_SetHead(p_parabag_UpdateStart_back stPara, uint16_t Type, uint16_t Len)
{
    stPara->head.header[0] = SETPARA_HEADER_0;
    stPara->head.header[1] = SETPARA_HEADER_1;
    stPara->head.header[2] = SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

// 升级开始帧尾设置
void UpdateStart_SetEnd(p_parabag_UpdateStart_back stPara, uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_UpdateStart_back)]={0};
    
    memcpy(Databuf, stPara, Len-3);
    
    stPara->ender.check = crc_verify_8bit(&Databuf[3], Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 升级发送帧头设置
void UpdateSend_SetHead(p_parabag_UpdateSend_back stPara, uint16_t Type, uint16_t Len)
{
    stPara->head.header[0] = SETPARA_HEADER_0;
    stPara->head.header[1] = SETPARA_HEADER_1;
    stPara->head.header[2] = SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

// 升级发送帧尾设置
void UpdateSend_SetEnd(p_parabag_UpdateSend_back stPara, uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_UpdateSend_back)]={0};
    
    memcpy(Databuf, stPara, Len-3);
    
    stPara->ender.check = crc_verify_8bit(&Databuf[3], Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 升级完成帧头设置
void UpdateEnd_SetHead(p_parabag_UpdateEnd_back stPara, uint16_t Type, uint16_t Len)
{
    stPara->head.header[0] = SETPARA_HEADER_0;
    stPara->head.header[1] = SETPARA_HEADER_1;
    stPara->head.header[2] = SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

// 升级完成帧尾设置
void UpdateEnd_SetEnd(p_parabag_UpdateEnd_back stPara, uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_UpdateEnd_back)]={0};
    
    memcpy(Databuf, stPara, Len-3);
    
    stPara->ender.check = crc_verify_8bit(&Databuf[3], Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 升级终止帧头设置
void UpdateStop_SetHead(p_parabag_UpdateStop_back stPara, uint16_t Type, uint16_t Len)
{
    stPara->head.header[0] = SETPARA_HEADER_0;
    stPara->head.header[1] = SETPARA_HEADER_1;
    stPara->head.header[2] = SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

// 升级终止帧尾设置
void UpdateStop_SetEnd(p_parabag_UpdateStop_back stPara, uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_UpdateStop_back)]={0};
    
    memcpy(Databuf, stPara, Len-3);
    
    stPara->ender.check = crc_verify_8bit(&Databuf[3], Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 参数回读帧头设置
void ReadPara_SetHead(p_parabag_ReadPara_back stPara, uint16_t Type, uint16_t Len)
{
    stPara->head.header[0] = SETPARA_HEADER_0;
    stPara->head.header[1] = SETPARA_HEADER_1;
    stPara->head.header[2] = SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

// 参数回读帧尾设置
void ReadPara_SetEnd(p_parabag_ReadPara_back stPara, uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_ReadPara_back)]={0};
    
    memcpy(Databuf, stPara, Len-3);
    
    stPara->ender.check = crc_verify_8bit(&Databuf[3], Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 从Flash读取参数
void ReadParaFromFlash(void)
{
    uint8_t ReadFlashDatabuf[sizeof(Setpara_Data)]={0};

    // 读取Flash中的参数
    ReadFlashByAddr(SAVE_SET_PARA_ADDR, ReadFlashDatabuf, sizeof(Setpara_Data));

    memcpy(&stSetPara, ReadFlashDatabuf, sizeof(Setpara_Data));

    // 检查参数有效性，如果无效则设置默认值
    if(stSetPara.Flag != 422)
    {
        memset(&stSetPara, 0, sizeof(Setpara_Data));
        stSetPara.Flag = 422;
        stSetPara.Setbaud = 4608;  // 默认波特率460800
        stSetPara.Setfre = SETPARA_DATAOUT_FPGA_FREQ;  // 默认频率

        memcpy(ReadFlashDatabuf, &stSetPara, sizeof(Setpara_Data));

        // 保存默认参数到Flash
        InitFlashAddr(SAVE_SET_PARA_ADDR - ADDR_FMC_SECTOR_6);
        WriteFlash(ReadFlashDatabuf, sizeof(Setpara_Data));
        EndWrite();
    }

    InitParaToAlgorithm();
}

// 将参数初始化到算法模块
void InitParaToAlgorithm(void)
{
    // 将参数同步到算法模块
    // 这里需要根据具体的算法接口进行实现
    
    // 示例：设置频率
    if(stSetPara.Setfre > 0)
    {
        hSetting.settingData.freq = stSetPara.Setfre;
    }
    
    // 示例：设置GNSS杆臂参数
    hSetting.settingData.param.gnssArmLength[0] = stSetPara.armX;
    hSetting.settingData.param.gnssArmLength[1] = stSetPara.armY;
    hSetting.settingData.param.gnssArmLength[2] = stSetPara.armZ;
    
    // 示例：设置天线安装角度
    hSetting.settingData.param.gnssAtt_from_vehicle[0] = stSetPara.angleX;
    hSetting.settingData.param.gnssAtt_from_vehicle[1] = stSetPara.angleY;
    hSetting.settingData.param.gnssAtt_from_vehicle[2] = stSetPara.angleZ;
    
    // 示例：设置车体杆臂参数
    hSetting.settingData.param.OBArmLength[0] = stSetPara.vectorX;
    hSetting.settingData.param.OBArmLength[1] = stSetPara.vectorY;
    hSetting.settingData.param.OBArmLength[2] = stSetPara.vectorZ;
    
    // 示例：设置安装角度偏差
    hSetting.settingData.param.OBAtt_from_vehicle[0] = stSetPara.pitch;
    hSetting.settingData.param.OBAtt_from_vehicle[1] = stSetPara.roll;
    hSetting.settingData.param.OBAtt_from_vehicle[2] = stSetPara.Course;
}

// 保存参数到Flash
void SaveParaToFlash(void)
{
    uint8_t WriteFlashDatabuf[sizeof(Setpara_Data)]={0};

    memcpy(WriteFlashDatabuf, &stSetPara, sizeof(Setpara_Data));

    // 使用项目中的Flash操作函数
    InitFlashAddr(SAVE_SET_PARA_ADDR - ADDR_FMC_SECTOR_6);
    WriteFlash(WriteFlashDatabuf, sizeof(Setpara_Data));
    EndWrite();
}

// 初始化参数设置模块
void SetParaBao_Init(void)
{
    // 从Flash读取参数
    ReadParaFromFlash();

    // 初始化升级相关标志
    g_UpdateBackFlag = 1;
    g_UpdateSuccessful = 0;
    g_ucSystemResetFlag = 0;
    g_UpdateFinishSuccessful = 0;
    g_VersionQueryFlag = 0;
    g_StartUpdateFirm = 0;
}

// 设置波特率
void SetParaBaud(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_Setbaud stBaud;
    memcpy(&stBaud, pdmauart->rxbuffer, sizeof(parabag_Setbaud));

    // 计算实际数据长度进行CRC校验
    uint16_t actualLen = sizeof(parabag_Setbaud);
    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], actualLen - 3 - 3);
    if(crc == stBaud.ender.check)
    {
        stSetPara.Setbaud = stBaud.info.SetbaudPara;
        stSendPara.info.BackFlag = 0x01;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    SendPara_SetHead(&stSendPara, SETPARA_TYPE1_baud, BaoLen);
    SendPara_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
    uart3sendmsg422((char*)SendDatabuf, BaoLen);

    delay_ms(500);
    // 重新配置UART波特率
    // 这里需要根据具体的UART配置函数进行实现
}

// 设置数据输出频率
void SetParaFrequency(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_Setfrequency stfrequency;
    memcpy(&stfrequency, pdmauart->rxbuffer, sizeof(parabag_Setfrequency));

    // 计算实际数据长度进行CRC校验
    uint16_t actualLen = sizeof(parabag_Setfrequency);
    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], actualLen - 3 - 3);
    if(crc == stfrequency.ender.check)
    {
        stSetPara.Setfre = stfrequency.info.SetfrePara;
        hSetting.settingData.freq = stSetPara.Setfre;
        stSendPara.info.BackFlag = 0x01;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    SendPara_SetHead(&stSendPara, SETPARA_TYPE1_frequency, BaoLen);
    SendPara_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
    uart3sendmsg422((char*)SendDatabuf, BaoLen);
}

// 软件升级开始命令
void SetParaUpdateStart(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateStart_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_UpdateStart_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateStart_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateStart_back));

    parabag_SetType stUpdateStart;
    memcpy(&stUpdateStart, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stUpdateStart.ender.check)
    {
        stSendPara.info.BackFlag = 0x01;

        stSendPara.info.Vermain = VERMAIN;
        stSendPara.info.Verminor = VERMINOR;
        stSendPara.info.Revision = REVISION;
        stSendPara.info.VerDate = VERYEAR*10000 + VERMONTH*100 + VERDAY;
        stSendPara.info.Plan = PLAN;
        stSendPara.info.Suffix = SUFFIX;
        stSendPara.info.Custom = CUSTOM;

        g_StartUpdateFirm = 1;
        g_UpdateSuccessful = 0;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    g_VersionQueryFlag = 1; // 版本查询

    stSendPara.info.Count++; // 帧计数

    delay_ms(100);

    UpdateStart_SetHead(&stSendPara, SETPARA_TYPE1_UPDATE_START, BaoLen);
    UpdateStart_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
    delay_ms(100);
}

// 发送升级包命令
void SetParaUpdateSend(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateSend_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_UpdateSend_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateSend_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateSend_back));
    g_UpdateBackFlag = 1;
    g_VersionQueryFlag = 0; // 版本查询

    parabag_UpdateSend stUpdateSend;
    memcpy(&stUpdateSend, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stUpdateSend.ender.check)
    {
        // 处理升级数据包
        // 这里需要实现具体的Flash写入逻辑
        stSendPara.info.BackFlag = 0x01;

        // 写入升级数据到Flash
        // uint32_t flash_addr = APP_LOADED_ADDR + stUpdateSend.info.BaoIndex * 128;
        // fmc_write_8bit(stUpdateSend.info.UpdateData, flash_addr, stUpdateSend.info.Length);
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    stSendPara.info.Count = stUpdateSend.info.Count;

    UpdateSend_SetHead(&stSendPara, SETPARA_TYPE1_UPDATE_SEND, BaoLen);
    UpdateSend_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

// 升级包完成命令
void SetParaUpdateEnd(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateEnd_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_UpdateEnd_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateEnd_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateEnd_back));

    parabag_SetType stUpdateEnd;
    memcpy(&stUpdateEnd, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stUpdateEnd.ender.check)
    {
        stSendPara.info.BackFlag = 0x01;
        g_UpdateFinishSuccessful = 1;
        g_ucSystemResetFlag = 1; // 设置系统复位标志
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    UpdateEnd_SetHead(&stSendPara, SETPARA_TYPE1_UPDATE_END, BaoLen);
    UpdateEnd_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);

    delay_ms(100);

    // 如果升级成功，延时后复位系统
    if(g_UpdateFinishSuccessful)
    {
        delay_ms(1000);
        NVIC_SystemReset(); // 系统复位
    }
}

// 升级终止命令
void SetParaUpdateStop(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateStop_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_UpdateStop_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateStop_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateStop_back));

    parabag_SetType stUpdateStop;
    memcpy(&stUpdateStop, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stUpdateStop.ender.check)
    {
        stSendPara.info.BackFlag = 0x01;
        g_StartUpdateFirm = 0; // 停止升级
        g_UpdateSuccessful = 0;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    UpdateStop_SetHead(&stSendPara, SETPARA_TYPE1_UPDATE_STOP, BaoLen);
    UpdateStop_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

//软件升级开始命令（从INS600-21A移植，同时用于版本查询）
void SetParaUpdateStart(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateStart_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_UpdateStart_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateStart_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateStart_back));

    parabag_SetType stUpdateStart;//接收
    memcpy(&stUpdateStart,pdmauart->rxbuffer,sizeof(parabag_SetType));

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3],sizeof(parabag_SetType) - 3 - 3);

    if(crc == stUpdateStart.ender.check)
    {
        stSendPara.info.BackFlag=0x01;

        stSendPara.info.Vermain = VERMAIN;
        stSendPara.info.Verminor = VERMINOR;
        stSendPara.info.Revision = REVISION;
        stSendPara.info.VerDate = VERYEAR*10000 + VERMONTH*100 + VERDAY;
        stSendPara.info.Plan = PLAN;
        stSendPara.info.Suffix = SUFFIX;
        stSendPara.info.Custom = CUSTOM;

        g_StartUpdateFirm=1;
        g_UpdateSuccessful=0;
    }
    else
    {
        stSendPara.info.BackFlag=0x02;
    }

    g_VersionQueryFlag=1;//版本查询

    stSendPara.info.Count++;//帧计数

    delay_ms(100);

    //配置帧头
    UpdateStart_SetHead(&stSendPara,SETPARA_TYPE1_UPDATE_START,BaoLen);
    //配置帧尾
    UpdateStart_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
    delay_ms(100);
}

// 版本查询命令（兼容旧接口）
void SetParaReadVersion(p_dmauart_t pdmauart)
{
    // 直接调用升级开始命令，因为它包含了版本查询功能
    SetParaUpdateStart(pdmauart);
}

//发送升级包帧头（从INS600-21A移植）
void UpdateSend_SetHead(p_parabag_UpdateSend_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//发送升级包帧尾（从INS600-21A移植）
void UpdateSend_SetEnd(p_parabag_UpdateSend_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//升级完成帧头（从INS600-21A移植）
void UpdateEnd_SetHead(p_parabag_UpdateEnd_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//升级完成帧尾（从INS600-21A移植）
void UpdateEnd_SetEnd(p_parabag_UpdateEnd_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//软件升级终止帧头（从INS600-21A移植）
void UpdateStop_SetHead(p_parabag_UpdateStop_back stPara,uint16_t Type,uint16_t Len)
{
    stPara->head.header[0]=SETPARA_HEADER_0;
    stPara->head.header[1]=SETPARA_HEADER_1;
    stPara->head.header[2]=SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

//软件升级终止帧尾（从INS600-21A移植）
void UpdateStop_SetEnd(p_parabag_UpdateStop_back stPara,uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};

    memcpy(Databuf,stPara,Len-3);

    stPara->ender.check = crc_verify_8bit(&Databuf[3],Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

//固件升级处理（从INS600-21A移植）
void ParaUpdateHandle(uint8_t *pucBuf,uint16_t usIndex,uint16_t usTotalBao,uint8_t ucLen)
{
    static uint32_t uiOffsetAddr=0,uiOffsetAddr1=0,uiLastBaoInDex=1,flag=0;
    uint8_t UpdateFlagBuff[5]={0};//升级被分区完成标志,升级完成后，保存此标志到FLASH指定位置，拷贝到运行区后，此标志擦除

    //if(uiLastBaoInDex == usIndex)
    if(((uiLastBaoInDex == usIndex)||(uiLastBaoInDex != usIndex-1))&&(0 !=usIndex))
    {
        return;
    }

    if(flag==0)
    {
        if(0 !=usIndex)//首帧帧索引不为0时，报错
        {
            g_UpdateBackFlag=2;//反馈标志(0x01-正常/02-异常)
            return;
        }
        else//检测到首帧帧索引为0时，才通过
        {
            flag=1;
        }
    }

    if(usIndex == 0)
    {
        uiOffsetAddr=0;
        Drv_FlashErase(APP_UPDATE_ADDRESS,1);
        Drv_FlashErase(APP_UPDATE_ADDRESS1,1);
        Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen, 0);
        uiOffsetAddr += ucLen;
    }
    else
    {
        Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen, 0);
        uiOffsetAddr += ucLen;
    }

    //升级完成后，保存升级标志和数据长度，并重启
    if(usIndex>=usTotalBao-1)
    {
        g_UpdateFinishSuccessful=1;
        UpdateFlagBuff[0] =0xC5;//保存升级标志
        //数据长度
        UpdateFlagBuff[4] = (uint8_t)(uiOffsetAddr>>24);//高位
        UpdateFlagBuff[3] = (uint8_t)(uiOffsetAddr>>16);//中高位
        UpdateFlagBuff[2] = (uint8_t)(uiOffsetAddr>>8);//中低位
        UpdateFlagBuff[1] = (uint8_t)(uiOffsetAddr);//低位

        Drv_FlashWrite(UpdateFlagBuff, APP_UPDATE_CFG_ADDR, sizeof(UpdateFlagBuff), 0);//保存升级标志

        uiOffsetAddr=0;

        //系统复位重启
        //Drv_SystemReset();
    }

    uiLastBaoInDex = usIndex;
}

//发送升级包命令（从INS600-21A移植）
void SetParaUpdateSend(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateSend_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_UpdateSend_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateSend_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateSend_back));
    g_UpdateBackFlag=1;
    g_VersionQueryFlag=0;//版本查询

    parabag_UpdateSend stUpdateSend;//接收
    memcpy(&stUpdateSend,pdmauart->rxbuffer,sizeof(parabag_UpdateSend));

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3],sizeof(parabag_UpdateSend) - 3 - 3);

    if(crc == stUpdateSend.ender.check)
    {
        ParaUpdateHandle(stUpdateSend.info.UpdateData,stUpdateSend.info.BaoIndex,stUpdateSend.info.TotalBao,stUpdateSend.info.Length);
        stSendPara.info.BackFlag=g_UpdateBackFlag;
        g_UpdateSuccessful=1;
    }
    else
    {
        stSendPara.info.BackFlag=0x02;
    }

    stSendPara.info.BaoIndex = stUpdateSend.info.BaoIndex;
    stSendPara.info.Count++;//帧计数

    if(stSendPara.info.BackFlag==0x02)
    {
        delay_ms(500);
        nvic_irq_enable(UART4_IRQn, 0, 0);
        gd_eval_com_init(UART4, stSetPara.Setbaud*100);
        usart_interrupt_enable(UART4, USART_INT_RBNE);
    }

    //配置帧头
    UpdateSend_SetHead(&stSendPara,SETPARA_TYPE1_UPDATE_SEND,BaoLen);
    //配置帧尾
    UpdateSend_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

//固件升级包完成命令（从INS600-21A移植）
void SetParaUpdateEnd(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateEnd_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_UpdateEnd_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateEnd_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateEnd_back));

    parabag_SetType stUpdateEnd;//接收
    memcpy(&stUpdateEnd,pdmauart->rxbuffer,sizeof(parabag_SetType));

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3],sizeof(parabag_SetType) - 3 - 3);
    if(crc == stUpdateEnd.ender.check)
    {
        if((g_UpdateFinishSuccessful==1)||(g_VersionQueryFlag==1))
        {
            stSendPara.info.BackFlag=0x01;

            g_StartUpdateFirm=0;
            g_ucSystemResetFlag=1;
        }
        else
        {
            stSendPara.info.BackFlag=0x02;
        }
    }
    else
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    UpdateEnd_SetHead(&stSendPara,SETPARA_TYPE1_UPDATE_END,BaoLen);
    //配置帧尾
    UpdateEnd_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);

    if((g_ucSystemResetFlag==1)&&(g_UpdateSuccessful==1))
    {
        delay_ms(1000);
        Drv_SystemReset();
    }
}

//固件升级终止命令（从INS600-21A移植）
void SetParaUpdateStop(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateStop_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_UpdateStop_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateStop_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateStop_back));

    parabag_SetType stUpdateStop;//接收
    memcpy(&stUpdateStop,pdmauart->rxbuffer,sizeof(parabag_SetType));

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3],sizeof(parabag_SetType) - 3 - 3);
    if(crc == stUpdateStop.ender.check)
    {
        stSendPara.info.BackFlag=0x01;

        g_StartUpdateFirm=0;
    }
    else
    {
        stSendPara.info.BackFlag=0x02;
    }

    //配置帧头
    UpdateStop_SetHead(&stSendPara,SETPARA_TYPE1_UPDATE_STOP,BaoLen);
    //配置帧尾
    UpdateStop_SetEnd(&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

//参数设置主流程控制（从INS600-21A移植并适配）
void UartDmaRecSetPara(p_dmauart_t pdmauart)
{
    unsigned short Type=0;	//报文类型

    memcpy(&Type,&pdmauart->rxbuffer[3],2);

    switch(Type)
    {
        case SETPARA_TYPE0_UPDATE_START://软件升级开始命令（同时用于版本查询）
             SetParaUpdateStart(pdmauart);
            break;

        case SETPARA_TYPE0_UPDATE_SEND:	//发送升级包命令
             SetParaUpdateSend(pdmauart);
            break;
        case SETPARA_TYPE0_UPDATE_END:	//升级包完成命令
             SetParaUpdateEnd(pdmauart);
            break;
        case SETPARA_TYPE0_UPDATE_STOP:	//升级终止命令
             SetParaUpdateStop(pdmauart);
            break;

        case SETPARA_TYPE0_readver:	//读取版本号（兼容旧协议）
             SetParaReadVersion(pdmauart);
            break;

        default:
            break;
    }
}

// 参数回读命令
void SetParaReadPara(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_ReadPara_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_ReadPara_back);
    uint8_t SendDatabuf[sizeof(parabag_ReadPara_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_ReadPara_back));

    parabag_SetType stReadPara;
    memcpy(&stReadPara, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stReadPara.ender.check)
    {
        stSendPara.info.BackFlag = 0x01;
        // 这里可以添加具体的参数回读逻辑
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    ReadPara_SetHead(&stSendPara, SETPARA_TYPE1_readpara, BaoLen);
    ReadPara_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

// 固化参数命令
void SetParaSolidify(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetType stSolidify;
    memcpy(&stSolidify, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stSolidify.ender.check)
    {
        // 保存参数到Flash
        SaveParaToFlash();
        stSendPara.info.BackFlag = 0x01;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    SendPara_SetHead(&stSendPara, SETPARA_TYPE1_solidify, BaoLen);
    SendPara_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

// 恢复出厂设置命令
void SetParaFactory(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetType stFactory;
    memcpy(&stFactory, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stFactory.ender.check)
    {
        // 恢复出厂设置
        memset(&stSetPara, 0, sizeof(Setpara_Data));
        stSetPara.Flag = 422;
        stSetPara.Setbaud = 4608;  // 默认波特率460800
        stSetPara.Setfre = SETPARA_DATAOUT_FPGA_FREQ;  // 默认频率

        SaveParaToFlash();
        InitParaToAlgorithm();

        stSendPara.info.BackFlag = 0x01;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    SendPara_SetHead(&stSendPara, SETPARA_TYPE1_factory, BaoLen);
    SendPara_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

// 参数设置主流程控制函数（移植自INS600-21A新协议）
void UartDmaRecSetPara(p_dmauart_t pdmauart)
{
    unsigned short Type = 0;  // 报文类型

    // 从接收缓冲区中提取命令类型
    memcpy(&Type, &pdmauart->rxbuffer[3], 2);

    switch(Type)
    {
        case SETPARA_TYPE0_output:          // 输出参数
            break;
        case SETPARA_TYPE0_baud:            // 波特率
            SetParaBaud(pdmauart);
            break;
        case SETPARA_TYPE0_frequency:       // 数据输出频率
            SetParaFrequency(pdmauart);
            break;
        case SETPARA_TYPE0_gnss:            // 设置GNSS杆臂参数
            // SetParaGnss(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_angle:           // 设置天线安装角度
            // SetParaAngle(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_vector:          // 惯导-后轮轴中心位置矢量
            // SetParaVector(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_deviation:       // 惯导角度安装偏差
            // SetParaDeviation(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_initvalue:       // GNSS初始值
            // SetParaGnssInitValue(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_coord:           // 设置用户坐标轴
            // SetParaCoord(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_offsetime:       // 设置静态测零偏时间
            // SetParaTime(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_transfer:        // 设置透传协议
            break;
        case SETPARA_TYPE0_solidify:        // 固化参数
            SetParaSolidify(pdmauart);
            break;
        case SETPARA_TYPE0_factory:         // 恢复出厂设置
            SetParaFactory(pdmauart);
            break;
        case SETPARA_TYPE0_setpara:         // 设置所有参数
            // SetParaAll(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_readpara:        // 参数回读
            SetParaReadPara(pdmauart);
            break;
        case SETPARA_TYPE0_readver:         // 读取版本号
            SetParaReadVersion(pdmauart);
            break;
        case SETPARA_TYPE0_gps:             // GPS中断类型
            // SetParaGpsType(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_type:            // 数据输出Type
            // SetParaDataOutType(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_debug:           // 是否开启Debug模式
            // SetParaDebugMode(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_gyro:            // 陀螺类型
            // SetParaGyroType(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_calibration:     // 标定参数
            // SetParaCalibration(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_temoffset:       // 温度补偿
            break;
        case SETPARA_TYPE0_KalmanQ:         // 卡尔曼滤波Q矩阵
            // SetParaKalmanQ(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_KalmanR:         // 卡尔曼滤波R矩阵
            // SetParaKalmanR(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_filter:          // 间接滤波校正系数
            // SetParaFilter(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_FactorGyro:      // 陀螺标定因数
            // SetParaFactorGyro(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_FactorAcc:       // 加计标定因数
            // SetParaFactorAcc(pdmauart);  // 待实现
            break;
        case SETPARA_TYPE0_UPDATE_START:    // 软件升级开始命令
            SetParaUpdateStart(pdmauart);
            break;
        case SETPARA_TYPE0_UPDATE_SEND:     // 发送升级包命令
            SetParaUpdateSend(pdmauart);
            break;
        case SETPARA_TYPE0_UPDATE_END:      // 升级包完成命令
            SetParaUpdateEnd(pdmauart);
            break;
        case SETPARA_TYPE0_UPDATE_STOP:     // 升级终止命令
            SetParaUpdateStop(pdmauart);
            break;
        default:
            // 未知命令类型，发送调试信息
            {
                char debugMsg[64];
                sprintf(debugMsg, "Unknown Command Type: 0x%04X\r\n", Type);
                uart4sendmsg(debugMsg, strlen(debugMsg));
            }
            break;
    }
}
