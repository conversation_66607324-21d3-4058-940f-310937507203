# 版本查询功能修复指南

## 🔍 问题分析

### 问题现象
- 上位机发送版本查询命令后显示"***开始查询设备版本信息***"
- 随后显示"接收版本查询响应失败,查询版本信息失败!"
- 弹出错误提示"接收版本查询响应失败,查询版本信息失败!"

### 根本原因
1. **版本信息字段未初始化** - `hSetting.ARM1_FW_Ver`、`hSetting.ARM2_FW_Ver`、`hSetting.FPGA_FW_Ver` 字段为空
2. **UART发送条件限制** - `uart4sendmsg`函数中的`gbilldebuguart4`条件判断阻止了数据发送
3. **协议响应不完整** - 旧协议和新协议的版本查询响应可能存在冲突

## 🔧 修复方案

### 1. 初始化版本信息字符串

**文件：** `INS370M-25J20240919/Source/src/INS_Init.c`

在`SetDefaultProductInfo()`函数中添加版本信息初始化：

```c
// 初始化版本信息字符串
strcpy(hSetting.ARM1_FW_Ver, "INS370M-ARM1-v1.0.1");
strcpy(hSetting.ARM2_FW_Ver, "INS370M-ARM2-v1.0.1");
strcpy(hSetting.FPGA_FW_Ver, "INS370M-FPGA-v1.0.1");
strcpy(hSetting.facilityType, "INS370M-25J20240919");
```

### 2. 移除UART发送限制

**文件：** `INS370M-25J20240919/Source/src/gd32f4xx_it.c`

修改`uart4sendmsg`函数，移除`gbilldebuguart4`条件限制：

```c
void uart4sendmsg(char *txbuf, int size)
{
    // 移除gbilldebuguart4条件限制，确保版本查询响应能正常发送
    while(gbtxcompleted == 0);
    gbtxcompleted = 0;
    nbr_data_to_send = size;
    memcpy(tx_buffer, txbuf, size);
    
#ifdef DEVICE_UART_SELECT_ISU4_NOU3        
    usart_interrupt_enable(UART4, USART_INT_TBE);        
#else        
    usart_interrupt_enable(UART3, USART_INT_TBE);        
#endif        
}
```

### 3. 增强旧协议版本查询响应

**文件：** `INS370M-25J20240919/Protocol/computerFrameParse.c`

在`comm_read_ver_rsp`函数中添加版本信息检查和双重发送：

```c
void comm_read_ver_rsp(uint16_t cmd)
{
    // 确保版本信息字符串不为空
    if(strlen(hSetting.ARM1_FW_Ver) == 0) {
        strcpy(hSetting.ARM1_FW_Ver, "INS370M-ARM1-v1.0.1");
    }
    if(strlen(hSetting.ARM2_FW_Ver) == 0) {
        strcpy(hSetting.ARM2_FW_Ver, "INS370M-ARM2-v1.0.1");
    }
    if(strlen(hSetting.FPGA_FW_Ver) == 0) {
        strcpy(hSetting.FPGA_FW_Ver, "INS370M-FPGA-v1.0.1");
    }
    
    // 构建响应帧...
    
    // 同时发送到UART3和UART4，确保上位机能收到
    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, frame);
    uart4sendmsg((char*)frame, len);  // 添加直接UART4发送
}
```

### 4. 增强新协议版本查询响应

**文件：** `INS370M-25J20240919/Protocol/SetParaBao.c`

在`SetParaReadVersion`函数中添加调试信息和版本信息检查：

```c
void SetParaReadVersion(p_dmauart_t pdmauart)
{
    // 确保版本信息字符串已初始化
    if(strlen(hSetting.ARM1_FW_Ver) == 0) {
        strcpy(hSetting.ARM1_FW_Ver, "INS370M-ARM1-v1.0.1");
    }
    // ... 其他版本信息检查
    
    // 发送调试信息
    char debugMsg[128];
    sprintf(debugMsg, "Version Query: CRC=%02X Expected=%02X\r\n", crc, stReadVer.ender.check);
    uart4sendmsg(debugMsg, strlen(debugMsg));
    
    // ... 构建和发送响应
    
    // 确认发送成功
    sprintf(debugMsg, "Version Response Sent Successfully\r\n");
    uart4sendmsg(debugMsg, strlen(debugMsg));
}
```

## 🧪 测试验证

### 1. 编译验证
```bash
# 编译项目，确保没有编译错误
# 检查所有修改的文件是否正确包含必要的头文件
```

### 2. 功能测试
1. **启动系统** - 观察串口输出，确认版本信息已正确初始化
2. **版本查询测试** - 使用上位机发送版本查询命令
3. **调试信息观察** - 查看串口输出的调试信息，确认：
   - 版本信息字符串不为空
   - CRC校验过程
   - 响应发送确认

### 3. 预期结果
- 上位机能正常接收到版本查询响应
- 显示正确的版本信息：
  - ARM1: INS370M-ARM1-v1.0.1
  - ARM2: INS370M-ARM2-v1.0.1
  - FPGA: INS370M-FPGA-v1.0.1
- 不再出现"版本查询失败"错误

## 📋 修改文件清单

1. `INS370M-25J20240919/Source/src/INS_Init.c` - 版本信息初始化
2. `INS370M-25J20240919/Source/src/gd32f4xx_it.c` - 移除UART发送限制
3. `INS370M-25J20240919/Protocol/computerFrameParse.c` - 增强旧协议响应
4. `INS370M-25J20240919/Protocol/SetParaBao.c` - 增强新协议响应

## 🔄 回滚方案

如果修复后出现问题，可以通过以下方式回滚：

1. **恢复uart4sendmsg条件判断**：
```c
if (gbilldebuguart4 == 0) {
    // 原有发送逻辑
}
```

2. **移除版本信息初始化代码**
3. **移除调试信息输出**

## 📝 注意事项

1. **版本号管理** - 后续可以通过修改版本宏定义来更新版本信息
2. **调试信息** - 生产版本中可以通过编译开关控制调试信息输出
3. **协议兼容性** - 确保新旧协议都能正常响应版本查询
4. **UART配置** - 确认UART3和UART4的配置正确，波特率匹配

## 🎯 后续优化建议

1. **版本信息动态化** - 考虑从Flash或配置文件读取版本信息
2. **错误处理增强** - 添加更详细的错误码和错误信息
3. **协议统一** - 考虑统一版本查询协议，避免双重处理
4. **自动化测试** - 建立版本查询功能的自动化测试用例
