# INS370M版本查询功能完整移植指南

## 🎯 移植目标

从INS600-21A(新协议)项目完整移植版本查询、参数设置、升级等功能到INS370M-25J20240919项目，确保上位机与下位机通信正常。

## 🔍 问题分析

### 问题现象
- 上位机发送版本查询命令后显示"***开始查询设备版本信息***"
- 随后显示"接收版本查询响应失败,查询版本信息失败!"
- 弹出错误提示"接收版本查询响应失败,查询版本信息失败!"

### 根本原因
1. **协议实现不完整** - INS370M项目缺少完整的新协议实现
2. **版本信息字段未初始化** - `hSetting.ARM1_FW_Ver`等字段为空
3. **UART发送条件限制** - `uart4sendmsg`函数中的条件判断阻止了数据发送
4. **Flash操作接口不匹配** - 升级功能需要的Flash操作函数缺失

## 🔧 完整移植方案

### 1. 完整替换SetParaBao.c文件

**文件：** `INS370M-25J20240919/Protocol/SetParaBao.c`

从INS600-21A(新协议)项目完整移植SetParaBao.c，包含：
- 完整的版本查询功能（`SetParaUpdateStart`）
- 升级功能（`SetParaUpdateSend`、`SetParaUpdateEnd`、`SetParaUpdateStop`）
- 参数设置功能（波特率、频率等）
- 帧头帧尾处理函数
- Flash操作适配函数

### 2. 初始化版本信息字符串

**文件：** `INS370M-25J20240919/Source/src/INS_Init.c`

在`SetDefaultProductInfo()`函数中添加版本信息初始化：

```c
// 初始化版本信息字符串
strcpy(hSetting.ARM1_FW_Ver, "INS370M-ARM1-v1.0.1");
strcpy(hSetting.ARM2_FW_Ver, "INS370M-ARM2-v1.0.1");
strcpy(hSetting.FPGA_FW_Ver, "INS370M-FPGA-v1.0.1");
strcpy(hSetting.facilityType, "INS370M-25J20240919");
```

### 3. 移除UART发送限制

**文件：** `INS370M-25J20240919/Source/src/gd32f4xx_it.c`

修改`uart4sendmsg`函数，移除`gbilldebuguart4`条件限制：

```c
void uart4sendmsg(char *txbuf, int size)
{
    // 移除gbilldebuguart4条件限制，确保版本查询响应能正常发送
    while(gbtxcompleted == 0);
    gbtxcompleted = 0;
    nbr_data_to_send = size;
    memcpy(tx_buffer, txbuf, size);

#ifdef DEVICE_UART_SELECT_ISU4_NOU3
    usart_interrupt_enable(UART4, USART_INT_TBE);
#else
    usart_interrupt_enable(UART3, USART_INT_TBE);
#endif
}
```

### 4. 增强旧协议版本查询响应

**文件：** `INS370M-25J20240919/Protocol/computerFrameParse.c`

在`comm_read_ver_rsp`函数中添加版本信息检查和双重发送：

```c
void comm_read_ver_rsp(uint16_t cmd)
{
    // 确保版本信息字符串不为空
    if(strlen(hSetting.ARM1_FW_Ver) == 0) {
        strcpy(hSetting.ARM1_FW_Ver, "INS370M-ARM1-v1.0.1");
    }
    if(strlen(hSetting.ARM2_FW_Ver) == 0) {
        strcpy(hSetting.ARM2_FW_Ver, "INS370M-ARM2-v1.0.1");
    }
    if(strlen(hSetting.FPGA_FW_Ver) == 0) {
        strcpy(hSetting.FPGA_FW_Ver, "INS370M-FPGA-v1.0.1");
    }

    // 构建响应帧...

    // 同时发送到UART3和UART4，确保上位机能收到
    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, frame);
    uart4sendmsg((char*)frame, len);  // 添加直接UART4发送
}
```

### 5. 添加升级地址定义

**文件：** `INS370M-25J20240919/Protocol/SetParaBao.h`

添加升级相关地址定义：

```c
// 升级相关地址定义（从INS600-21A移植）
#define APP_UPDATE_ADDRESS                      (0x08100000)  // 升级区域地址
#define APP_UPDATE_ADDRESS1                     (0x08180000)  // 升级区域地址1
#define APP_UPDATE_CFG_ADDR                     (0x081F0000)  // 升级配置地址
```

### 6. Flash操作适配

**文件：** `INS370M-25J20240919/Protocol/SetParaBao.c`

添加Flash操作适配函数：

```c
// Flash操作适配函数（适配INS600-21A的Flash接口到INS370M）
void Drv_FlashErase(uint32_t addr, uint8_t flag)
{
    // 使用INS370M的Flash擦除函数
    // 需要根据实际的Flash操作函数进行适配
}

void Drv_FlashWrite(uint8_t* data, uint32_t addr, uint16_t len, uint8_t flag)
{
    // 使用INS370M的Flash写入函数
    // 需要根据实际的Flash操作函数进行适配
}

void Drv_SystemReset(void)
{
    // 使用INS370M的系统复位函数
    Sys_Soft_Reset();
}
```

## 🧪 测试验证

### 1. 编译验证
```bash
# 编译项目，确保没有编译错误
# 检查所有修改的文件是否正确包含必要的头文件
```

### 2. 功能测试
1. **启动系统** - 观察串口输出，确认版本信息已正确初始化
2. **版本查询测试** - 使用上位机发送版本查询命令
3. **调试信息观察** - 查看串口输出的调试信息，确认：
   - 版本信息字符串不为空
   - CRC校验过程
   - 响应发送确认

### 3. 预期结果
- 上位机能正常接收到版本查询响应
- 显示正确的版本信息：
  - ARM1: INS370M-ARM1-v1.0.1
  - ARM2: INS370M-ARM2-v1.0.1
  - FPGA: INS370M-FPGA-v1.0.1
- 不再出现"版本查询失败"错误

## 📋 完整移植文件清单

### 主要修改文件：
1. **`INS370M-25J20240919/Protocol/SetParaBao.c`** - 完整替换为INS600-21A版本
   - 添加完整的版本查询功能
   - 添加升级功能（开始、发送、完成、终止）
   - 添加参数设置功能
   - 添加Flash操作适配函数

2. **`INS370M-25J20240919/Protocol/SetParaBao.h`** - 更新头文件
   - 添加升级地址定义
   - 添加函数声明
   - 添加数据结构定义

3. **`INS370M-25J20240919/Source/src/INS_Init.c`** - 版本信息初始化
   - 在系统初始化时设置版本字符串

4. **`INS370M-25J20240919/Source/src/gd32f4xx_it.c`** - 移除UART发送限制
   - 确保版本查询响应能正常发送

5. **`INS370M-25J20240919/Protocol/computerFrameParse.c`** - 增强旧协议响应
   - 添加版本信息检查
   - 添加双重发送机制

### 新增功能：
- ✅ **版本查询功能** - 通过`SETPARA_TYPE0_UPDATE_START`命令实现
- ✅ **升级功能** - 完整的固件升级流程
- ✅ **参数设置功能** - 波特率、频率等参数设置
- ✅ **Flash操作适配** - 适配INS370M的Flash操作接口
- ✅ **协议兼容性** - 同时支持新旧协议

## 🔄 回滚方案

如果修复后出现问题，可以通过以下方式回滚：

1. **恢复uart4sendmsg条件判断**：
```c
if (gbilldebuguart4 == 0) {
    // 原有发送逻辑
}
```

2. **移除版本信息初始化代码**
3. **移除调试信息输出**

## 📝 注意事项

1. **版本号管理** - 后续可以通过修改版本宏定义来更新版本信息
2. **调试信息** - 生产版本中可以通过编译开关控制调试信息输出
3. **协议兼容性** - 确保新旧协议都能正常响应版本查询
4. **UART配置** - 确认UART3和UART4的配置正确，波特率匹配

## 🎯 后续优化建议

1. **版本信息动态化** - 考虑从Flash或配置文件读取版本信息
2. **错误处理增强** - 添加更详细的错误码和错误信息
3. **协议统一** - 考虑统一版本查询协议，避免双重处理
4. **自动化测试** - 建立版本查询功能的自动化测试用例
