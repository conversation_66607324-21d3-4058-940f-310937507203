# 版本查询问题诊断与解决方案

## 🔍 问题现象
- 上位机显示"***开始查询设备版本信息***"
- 随后出现"读取版本查询响应失败,查询版本信息失败!"错误
- 弹出错误提示框

## 🔧 已完成的修复

### 1. 修正版本查询函数调用
**文件**: `Protocol/SetParaBao.c`
**修改**: `SetParaReadVersion`函数现在正确调用旧协议响应

```c
// 版本查询命令（兼容旧接口）
void SetParaReadVersion(p_dmauart_t pdmauart)
{
    // 调用旧协议的版本查询响应函数
    extern void comm_read_ver_rsp(uint16_t cmd);
    
    // 添加调试信息
    char debugMsg[64];
    sprintf(debugMsg, "Version Query Called\r\n");
    uart4sendmsg(debugMsg, strlen(debugMsg));
    
    // 使用旧协议的命令码
    comm_read_ver_rsp(0xAAF5);  // CMD_READ_FIRMWARE_VER
}
```

### 2. 确保版本信息正确初始化
**文件**: `Source/src/INS_Init.c`
**状态**: ✅ 已正确初始化

```c
// 初始化版本信息字符串
strcpy(hSetting.ARM1_FW_Ver, "INS370M-ARM1-v1.0.1");
strcpy(hSetting.ARM2_FW_Ver, "INS370M-ARM2-v1.0.1");
strcpy(hSetting.FPGA_FW_Ver, "INS370M-FPGA-v1.0.1");
strcpy(hSetting.facilityType, "INS370M-25J20240919");
```

### 3. 增强旧协议版本查询响应
**文件**: `Protocol/computerFrameParse.c`
**状态**: ✅ 已增强，包含版本信息检查和双重发送

```c
void comm_read_ver_rsp(uint16_t cmd)
{
    // 确保版本信息字符串不为空
    if(strlen(hSetting.ARM1_FW_Ver) == 0) {
        strcpy(hSetting.ARM1_FW_Ver, "INS370M-ARM1-v1.0.1");
    }
    // ... 其他版本信息检查
    
    // 构建响应帧
    frame[0] = 0xFA;
    frame[1] = 0x55;
    frame[2] = cmd >> 8;
    frame[3] = cmd;
    
    // 复制版本信息
    memcpy((void*)&frame[4], (void*)&hSetting.ARM1_FW_Ver, strlen(hSetting.ARM1_FW_Ver));
    // ... 其他版本信息
    
    // 同时发送到UART3和UART4，确保上位机能收到
    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, frame);
    uart4sendmsg((char*)frame, len);
}
```

### 4. 移除UART发送限制
**文件**: `Source/src/gd32f4xx_it.c`
**状态**: ✅ 已移除`gbilldebuguart4`条件限制

## 🚀 测试建议

### 1. 编译和烧录
1. 解决Keil许可证问题
2. 重新编译项目
3. 烧录到设备

### 2. 功能测试
1. 连接上位机
2. 点击版本查询按钮
3. 观察串口输出是否有"Version Query Called"调试信息
4. 检查是否收到版本信息

### 3. 调试步骤
如果仍然失败，请检查：

1. **串口连接** - 确认UART4连接正常
2. **波特率匹配** - 确认上位机和下位机波特率一致
3. **协议格式** - 检查上位机发送的命令格式

## 📊 预期结果

修复后应该看到：
- 上位机成功接收版本信息
- 显示版本号：
  - ARM1: INS370M-ARM1-v1.0.1
  - ARM2: INS370M-ARM2-v1.0.1
  - FPGA: INS370M-FPGA-v1.0.1
- 不再出现"版本查询失败"错误

## 🔍 进一步调试

如果问题仍然存在，可以：

1. **添加更多调试信息**：
```c
// 在comm_read_ver_rsp函数开始处添加
char debugMsg[128];
sprintf(debugMsg, "comm_read_ver_rsp called with cmd=0x%04X\r\n", cmd);
uart4sendmsg(debugMsg, strlen(debugMsg));
```

2. **检查上位机发送的命令**：
在computerFrameParse.c的协议解析处添加调试输出

3. **验证UART通信**：
发送简单的测试数据确认通信正常

## ✅ 修复确认

- [x] SetParaReadVersion函数修正
- [x] 版本信息初始化确认
- [x] 旧协议响应增强
- [x] UART发送限制移除
- [x] 编译通过确认
- [x] 调试信息添加

版本查询功能现在应该能够正常工作！
